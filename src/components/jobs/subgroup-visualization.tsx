import { useState, useEffect, useRef, useCallback } from 'react';
import { Subgroup } from './job-history-item';
import { ChevronRight, ChevronDown, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";

interface SubgroupNode extends Subgroup {
  id: string;
  expanded: boolean;
  pattern: string[];
  level: number;
}

interface Connection {
  from: string;
  to: string;
}

interface SubgroupVisualizationProps {
  subgroups: Subgroup[];
  totalTP: number;
  totalFP: number;
}
export function SubgroupVisualization({ subgroups, totalTP, totalFP }: SubgroupVisualizationProps): JSX.Element {
  const [nodes, setNodes] = useState<SubgroupNode[]>([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set([]));
  const [visibleNodes, setVisibleNodes] = useState<SubgroupNode[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [minUrrRate, setMinUrrRate] = useState<number>(0);
  const [maxUrrRate, setMaxUrrRate] = useState<number>(1);
  const [showCriticalSubgroups, setShowCriticalSubgroups] = useState<boolean>(false);
  const [showingSpecificPath, setShowingSpecificPath] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const nodeRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Parse pattern from description
  const parsePattern = (description: string): string[] => {
    if (description.includes('[') && description.includes(']')) {
      const patternPart = description.split('[')[1].split(']')[0];
      return patternPart.split(', ');
    }
    return [];
  };

  // Helper function to check if one node is a parent of another
  const isParentOf = (parentNode: SubgroupNode, childNode: SubgroupNode, requireDirectChild: boolean = false) => {
    // Basic level check
    if (childNode.level <= parentNode.level) return false;

    // For direct children, require exactly one more element
    if (requireDirectChild) {
      return isDirectParentPattern(parentNode.pattern, childNode.pattern);
    }

    // For any descendant, just require the subset relationship
    return isPatternSubset(parentNode.pattern, childNode.pattern);
  };

  // Utility functions for pattern comparison
const isPatternSubset = (parentPattern: string[], childPattern: string[]): boolean => {
  const parentSet = new Set(parentPattern);
  const childSet = new Set(childPattern);

  // Basic pattern checks
  if (parentSet.size === 0 || childSet.size === 0) return false;

  // Check if parent pattern is a subset of child pattern
  return [...parentSet].every(item => childSet.has(item));
};

const isDirectParentPattern = (parentPattern: string[], childPattern: string[]): boolean => {
  const parentSet = new Set(parentPattern);
  const childSet = new Set(childPattern);

  return isPatternSubset(parentPattern, childPattern) &&
         childSet.size - parentSet.size === 1;
};

  // Initialize nodes from subgroups
  useEffect(() => {
    if (!subgroups || subgroups.length === 0) return;

    // Create root node
    const rootNode: SubgroupNode = {
      id: 'root',
      description: 'Root',
      target: '',
      pattern_length: 0,
      quality: 0,
      urr_rate: totalTP / (totalTP + totalFP),
      impact_radius_relative: 100,
      tp: totalTP,
      fp: totalFP,
      TP: totalTP,
      FP: totalFP,
      expanded: true,
      pattern: [],
      level: 0
    };
    // Root node is initially collapsed, so we don't need to add it to expandedNodes here
    // Create nodes from subgroups
    const subgroupNodes = subgroups.map((sg, index) => {
      return {
        id: `node_${index}`,
        ...sg,
        expanded: false,
        pattern: parsePattern(sg.description),
        level: sg.pattern_length
      };
    });

    // Combine root and subgroup nodes
    const allNodes = [rootNode, ...subgroupNodes];

    // Calculate min and max URR rates for color scaling
    const urrRates = subgroups.map(sg => sg.urr_rate);
    if (urrRates.length > 0) {
      setMinUrrRate(Math.min(...urrRates));
      setMaxUrrRate(Math.max(...urrRates));
    }

    setNodes(allNodes);
  }, [subgroups, totalTP, totalFP]);

  // Consolidated function to determine visible nodes and their connections
  const calculateVisibleNodesAndConnections = useCallback(() => {
    //If there are no nodes, returns empty arrays
    if (nodes.length === 0) return { visible: [], nodeConnections: [] };

    // Get all visible nodes based on expanded state
    const visible: SubgroupNode[] = [];
    const nodeConnections: Connection[] = [];

    // Critical subgroups mode - show only critical nodes
    if (showCriticalSubgroups) {
      // Find all critical nodes (orange and red)
      const criticalNodes = nodes.filter(node => isNodeCritical(node));

      // Sort by URR rate descending to show most critical first
      criticalNodes.sort((a, b) => b.urr_rate - a.urr_rate);

      // Add all critical nodes to visible nodes
      visible.push(...criticalNodes);

      // No connections in critical subgroups mode
      return { visible, nodeConnections };
    }

    // Normal mode - hierarchical view
    // Always show root
    const rootNode = nodes.find(n => n.id === 'root');
    if (rootNode) visible.push(rootNode);

    // Check if we're showing a specific path from a critical node click
    if (showingSpecificPath) {
      // We're showing all possible parent paths - only show nodes in the expanded set
      const pathNodeIds = new Set(Array.from(expandedNodes));

      // Add all nodes in the paths to visible nodes
      nodes.filter(node => pathNodeIds.has(node.id)).forEach(node => {
        if (!visible.some(v => v.id === node.id)) {
          visible.push(node);
        }
      });

      // Group nodes by level for easier processing
      const nodesByLevel = new Map<number, SubgroupNode[]>();

      visible.forEach(node => {
        if (!nodesByLevel.has(node.level)) {
          nodesByLevel.set(node.level, []);
        }
        nodesByLevel.get(node.level)?.push(node);
      });

      // Process each level to create connections
      const maxLevel = Math.max(...visible.map(n => n.level));

      // Connect root to level 1 nodes
      const level1Nodes = nodesByLevel.get(1) || [];
      level1Nodes.forEach(node => {
        nodeConnections.push({ from: 'root', to: node.id });
      });

      // Connect each level to the next
      for (let level = 1; level < maxLevel; level++) {
        const currentLevelNodes = nodesByLevel.get(level) || [];
        const nextLevelNodes = nodesByLevel.get(level + 1) || [];

        // Check each potential parent-child relationship
        currentLevelNodes.forEach(parent => {
          nextLevelNodes.forEach(child => {
            // Connect if parent is a direct parent of child
            if (isDirectParentPattern(parent.pattern, child.pattern)) {
              nodeConnections.push({ from: parent.id, to: child.id });
            }
          });
        });
      }

      return { visible, nodeConnections };
    }
    // Standard hierarchical view (when no specific path is selected)
    // If root is expanded, show level 1 nodes
    if (expandedNodes.has('root')) {
      const level1Nodes = nodes.filter(n => n.level === 1);
      visible.push(...level1Nodes);

      // Add connections from root to level 1
      level1Nodes.forEach(node => {
        nodeConnections.push({ from: 'root', to: node.id });
      });
    }

    // Build a map of expanded nodes by level for easier lookup
    const expandedNodesByLevel = new Map<number, SubgroupNode[]>();

    // organizing expanded nodes by their level in the hierarchy.
    Array.from(expandedNodes)
      .filter(id => id !== 'root')
      .map(id => nodes.find(n => n.id === id))
      .filter((node): node is SubgroupNode => node !== undefined)
      .forEach(node => {
        if (!expandedNodesByLevel.has(node.level)) {
          expandedNodesByLevel.set(node.level, []);
        }
        expandedNodesByLevel.get(node.level)?.push(node);
      });

    // Process each level in order
    const maxLevel = Math.max(...nodes.map(n => n.level));

    for (let level = 1; level < maxLevel; level++) {
      const expandedNodesAtLevel = expandedNodesByLevel.get(level) || [];
      // For each expanded node at this level
      expandedNodesAtLevel.forEach(expandedNode => {
        // Find direct children (one level deeper)
        const children = nodes.filter(n => isParentOf(expandedNode, n, true));

        // Add children to visible nodes
        children.forEach(child => {
          if (!visible.some(v => v.id === child.id)) {
            visible.push(child);
          }
          // Add connection from expanded node to child
          nodeConnections.push({ from: expandedNode.id, to: child.id });
        });
      });
    }

    // This handles cases where a node has multiple parents
    for (let level = 2; level <= maxLevel; level++) {
      const nodesAtLevel = visible.filter(n => n.level === level);
      const potentialParents = visible.filter(n => n.level === level - 1);

      nodesAtLevel.forEach(node => {
        potentialParents.forEach(parent => {
          // Check if parentPattern is a subset of nodePattern and the difference is exactly one element
          if (isDirectParentPattern(parent.pattern, node.pattern)) {
            // Add connection if not already there
            if (!nodeConnections.some(conn => conn.from === parent.id && conn.to === node.id)) {
              nodeConnections.push({ from: parent.id, to: node.id });
            }
          }
        });
      });
    }

    return { visible, nodeConnections };
  }, [nodes, expandedNodes, isParentOf]);

  // Effect to update visible nodes and connections
  useEffect(() => {
    const { visible, nodeConnections } = calculateVisibleNodesAndConnections();
    setVisibleNodes(visible);
    setConnections(nodeConnections);
  }, [calculateVisibleNodesAndConnections]);

  // Helper function to find descendants of a node used in toggle function below
  const findDescendants = (parentNode: SubgroupNode, processNode: (nodeId: string) => void) => {
    // Find direct children
    const children = nodes.filter(n => isParentOf(parentNode, n, false));

    // Process each child
    children.forEach(child => {
      processNode(child.id);
      // Recursively find descendants
      findDescendants(child, processNode);
    });
  };
  // Toggle node expansion function
  const toggleNode = (nodeId: string) => {
    // Exit specific path mode when user interacts with normal view
    if (showingSpecificPath) {
      setShowingSpecificPath(false);
    }

    setExpandedNodes(prev => {
      // Creating a new copy of the expanded nodes Set before making any changes to it
      const newSet = new Set(prev);

      // Special case for root node: if already expanded, collapse everything
      if (nodeId === 'root' && newSet.has('root')) {
        // Collapse everything including root
        return new Set([]);
      }
      // If node is already expanded, collapse it and all its descendants
      if (newSet.has(nodeId)) {
        const nodeToCollapse = nodes.find(n => n.id === nodeId);

        if (nodeToCollapse) {
          // Remove the node itself
          newSet.delete(nodeId);

          // Find and remove all descendants
          //This ensures that when a parent is collapsed, all its children are hidden too
          findDescendants(nodeToCollapse, (childId) => {
            newSet.delete(childId);
          });
        }
      } else {
        // If clicking on a node at the same level as other expanded nodes,
        // collapse those other nodes first
        const clickedNode = nodes.find(n => n.id === nodeId);
        if (clickedNode) {
          // Find all other expanded nodes at the same level
          const sameLevel = Array.from(newSet)
            .map(id => nodes.find(n => n.id === id))
            .filter(n => n && n.level === clickedNode.level && n.id !== nodeId);

          // Remove those nodes and their descendants
          sameLevel.forEach(node => {
            if (node) {
              newSet.delete(node.id);

              // Find and remove all descendants
              findDescendants(node, (childId) => {
                newSet.delete(childId);
              });
            }
          });
        }
        // Now expand the clicked node
        newSet.add(nodeId);
      }
      return newSet;
    });
  };
  // Get color based on URR rate - matching the PDF graph coloring exactly
  const getNodeColorHex = (urrRate: number) => {
    // For root node, use white background
    if (urrRate === totalTP / (totalTP + totalFP)) {
      return 'white';
    }
    if (maxUrrRate === minUrrRate) {
      // Default to a yellowish color if all values are the same
      return '#ffcc33';
    }
    // Normalize URR rate between 0 and 1
    const normalized = (urrRate - minUrrRate) / (maxUrrRate - minUrrRate);

    // More gradual color mapping from green (low URR) to red (high URR)
    if (normalized < 0.2) {
      return '#4CAF50';      // Green
    } else if (normalized < 0.4) {
      return '#8BC34A';      // Light Green
    } else if (normalized < 0.5) {
      return '#CDDC39';      // Lime/Yellow-Green
    } else if (normalized < 0.6) {
      return '#FFEB3B';      // Yellow
    } else if (normalized < 0.7) {
      return '#FFC107';      // Amber
    } else if (normalized < 0.8) {
      return '#FF9800';      // Orange
    } else if (normalized < 0.9) {
      return '#FF5722';      // Deep Orange
    } else {
      return '#F44336';      // Red
    }
  };

  // Function to check if a node is critical (orange or red)
  const isNodeCritical = (node: SubgroupNode) => {
    // Skip the root node
    if (node.id === 'root') return false;
    // Normalize URR rate between 0 and 1
    const normalized = (node.urr_rate - minUrrRate) / (maxUrrRate - minUrrRate);

    // Node is critical if it's orange or red
    return normalized >= 0.8;
  };

  // Function to toggle critical subgroups view
  const toggleCriticalSubgroups = () => {
    // Reset specific path mode when switching views
    setShowingSpecificPath(false);
    // When switching from critical to normal view, reset expanded nodes
    if (showCriticalSubgroups) {
      // Going from critical to normal view - collapse all nodes
      setExpandedNodes(new Set([]));
    }
    setShowCriticalSubgroups(prev => !prev);
  };

  // Function to find all parent paths from root to a specific node
  const findAllParentPaths = (targetNodeId: string): string[] => {
    const targetNode = nodes.find(n => n.id === targetNodeId);
    if (!targetNode || targetNode.id === 'root') return ['root'];

    // Start with the target node and root
    const nodesToInclude = new Set([targetNodeId, 'root']);

    // Find all direct parents of the target node
    const findDirectParents = (nodeId: string) => {
      const node = nodes.find(n => n.id === nodeId);
      if (!node || node.id === 'root') return;

      // Find all direct parent nodes for this node
      const directParents = nodes.filter(n =>
        n.id !== node.id &&
        n.level < node.level &&
        isDirectParentPattern(n.pattern, node.pattern)
      );

      // Add all direct parents to the set
      directParents.forEach(parent => {
        nodesToInclude.add(parent.id);
        // Recursively find parents of each parent
        findDirectParents(parent.id);
      });
    };

    // Start the recursive search from the target node
    findDirectParents(targetNodeId);

    return Array.from(nodesToInclude);
  };

  // Function to handle click on a critical subgroup node
  const handleCriticalNodeClick = (nodeId: string) => {
    // Find all parent paths from root to this node
    const nodePaths = findAllParentPaths(nodeId);

    // Switch to normal view
    setShowCriticalSubgroups(false);

    // Enable specific path mode
    setShowingSpecificPath(true);

    // Set expanded nodes to include all nodes in all possible parent paths
    setExpandedNodes(new Set(nodePaths));
  };
  // Render SVG arrows for connections
  const renderConnections = () => {
    const container = containerRef.current;
    if (!container) return null;

    const containerRect = container.getBoundingClientRect();

    // Use a single SVG container for all connections
    return (
      <svg className="absolute top-0 left-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#444" />
          </marker>
        </defs>
        {connections
          .filter(conn => nodeRefs.current[conn.from] && nodeRefs.current[conn.to])
          .map((conn, index) => {
            const fromNode = nodeRefs.current[conn.from]!;
            const toNode = nodeRefs.current[conn.to]!;
            const fromRect = fromNode.getBoundingClientRect();
            const toRect = toNode.getBoundingClientRect();

            // Calculate start, end, and control points for the curve
            const fromX = fromRect.right - containerRect.left;
            const fromY = fromRect.top + fromRect.height / 2 - containerRect.top;
            const toX = toRect.left - containerRect.left;
            const toY = toRect.top + toRect.height / 2 - containerRect.top;
            const midX = fromX + (toX - fromX) / 2;

            return (
              <path
                key={`conn-${index}`}
                d={`M ${fromX} ${fromY} C ${midX} ${fromY}, ${midX} ${toY}, ${toX} ${toY}`}
                stroke="#444"
                strokeWidth="2"
                fill="none"
                markerEnd="url(#arrowhead)"
              />
            );
          })}
      </svg>
    );
  };

  return (
    <div
    //Tracks the container element that holds the entire visualization
      ref={containerRef}
      className="relative w-full overflow-auto p-10 min-h-[600px]"
      style={{ overflowX: 'auto' }}
    >
      {/* Button to toggle critical subgroups view */}
      <div className="mb-6 flex justify-between items-center">
        <Button
          onClick={toggleCriticalSubgroups}
          className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700 text-white"
        >
          <AlertTriangle className="h-4 w-4" />
          {showCriticalSubgroups ? "Show Normal View" : "Show Critical Subgroups"}
        </Button>
        {showCriticalSubgroups && (
          <div className="text-sm text-gray-600">
            Showing critical subgroups. Click on any node to view its specific path in normal view.
          </div>
        )}
      </div>

      {/* Render connections with arrow definitions - only in normal mode */}
      {!showCriticalSubgroups && renderConnections()}

      {/* Node container */}
      {showCriticalSubgroups ? (
        // Grid layout with responsive columns for critical subgroups mode
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-y-2 gap-x-1">
          {visibleNodes.map(node => (
            <div
              key={node.id}
              ref={el => nodeRefs.current[node.id] = el}
              className="relative p-1 rounded-lg border border-gray-300 shadow-md cursor-pointer transition-all duration-200 hover:shadow-lg flex flex-col w-[230px] h-[185px] mx-auto"
              style={{
                backgroundColor: getNodeColorHex(node.urr_rate)
              }}
              onClick={() => handleCriticalNodeClick(node.id)}
            >
              <div className="font-medium mb-0.5 flex-shrink-0 max-h-[60px] overflow-auto px-1">
                <div className="text-sm leading-tight">
                  {node.description}
                </div>
              </div>

              <div className="text-xs border-t pt-0.5 flex-grow flex flex-col justify-between">
                <div className="grid grid-cols-2 px-1">
                  <div className="text-sm font-semibold">Quality:</div>
                  <div className="text-sm font-semibold">{node.quality.toFixed(4)}</div>

                  <div className="text-sm font-semibold">URR Rate:</div>
                  <div className="text-sm font-semibold">{node.urr_rate.toFixed(4)}</div>

                  <div className="text-sm font-semibold">Impact:</div>
                  <div className="text-sm font-semibold">{node.impact_radius_relative.toFixed(2)}%</div>

                  <div className="text-sm font-semibold">true positives:</div>
                  <div className="text-sm font-semibold">{node.tp}</div>
                </div>
                <div className="text-black-600 text-center text-xs mt-1 border-t pt-1">
                  Click to view path in normal view
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        // Grid layout for normal hierarchical mode
        <div className="grid grid-flow-col gap-40 auto-cols-max">
          {/* Group nodes by level
          Makes sure level 0 comes before level 1, etc.*/}
          {Array.from(new Set(visibleNodes.map(node => node.level))).sort((a, b) => a - b).map(level => (
            <div key={`level-${level}`} className="flex flex-col gap-20 items-center">
              {visibleNodes.filter(node => node.level === level).map(node => (
                <div
                  key={node.id}
                  //Stores references to each individual node element in the graph
                  ref={el => nodeRefs.current[node.id] = el}
                  className={`relative p-2 rounded-lg border border-gray-300 shadow-md w-[180px] h-[180px] ${showingSpecificPath && node.id !== 'root' ? 'cursor-default' : 'cursor-pointer'} transition-all duration-200 hover:shadow-lg`}
                  style={{
                    backgroundColor: getNodeColorHex(node.urr_rate)
                  }}
                  onClick={() => {
                    // In specific path view, only allow clicking on root node
                    if (showingSpecificPath && node.id !== 'root') {
                      return; // Disable click for non-root nodes in specific path view
                    }
                    toggleNode(node.id);
                  }}
                >
                  <div className="flex items-center gap-1 font-medium mb-1">
                    {expandedNodes.has(node.id) ? (
                      <ChevronDown className="h-4 w-4 flex-shrink-0" />
                    ) : (
                      <ChevronRight className="h-4 w-4 flex-shrink-0" />
                    )}

                    {node.id === 'root' ? (
                      <span className="flex items-center gap-1">
                        Root Node
                        {expandedNodes.has('root') ? (
                          <span className="text-xs text-blue-600 ml-1">(click to collapse all)</span>
                        ) : (
                          <span className="text-xs text-blue-600 ml-1">(click to expand)</span>
                        )}
                      </span>
                    ) : (
                      <div className="max-h-16 overflow-y-auto text-sm leading-tight custom-scrollbar">
                        {node.description}
                      </div>
                    )}
                  </div>

                  <div className="text-xs border-t pt-0.5">
                    <div className="grid grid-cols-2 gap-x-1">
                      <div>Quality:</div>
                      <div className="font-semibold">{node.quality.toFixed(4)}</div>
                      <div>URR Rate:</div>
                      <div className="font-semibold">{node.urr_rate.toFixed(4)}</div>
                      <div>Impact:</div>
                      <div className="font-semibold">{node.impact_radius_relative.toFixed(2)}%</div>
                      <div>True Positives:</div>
                      <div className="font-semibold">{node.tp}</div>
                    </div>
                    {!showingSpecificPath && (
                      <div className="text-blue-600 text-center text-xs -mt-1">
                        {expandedNodes.has(node.id) ? "Click to collapse" : "Click to expand"}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
